"use client";

import React, { useState } from 'react';
import { useAuth } from '@/lib/auth-context';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Loader2, Trash2, AlertTriangle, Shield } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { logger } from '@/lib/logger';

export function AccountDeletionSection() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');
  const [reason, setReason] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [step, setStep] = useState<'warning' | 'confirmation' | 'final'>('warning');

  const handleDeleteAccount = async () => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "You must be logged in to delete your account.",
      });
      return;
    }

    if (confirmationText !== 'DELETE MY ACCOUNT') {
      toast({
        variant: "destructive",
        title: "Confirmation Required",
        description: "Please type 'DELETE MY ACCOUNT' exactly to confirm.",
      });
      return;
    }

    setIsDeleting(true);
    
    try {
      logger.log('[Account Deletion] Starting account deletion for user:', user.id);
      
      const response = await fetch('/api/user/delete-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          confirmationText,
          reason: reason.trim() || undefined
        }),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to delete account');
      }

      logger.log('[Account Deletion] Account deletion successful');

      // Show success message
      toast({
        title: "Account Deleted",
        description: result.message || "Your account has been permanently deleted.",
        duration: 5000,
      });

      // Close dialog and logout
      setShowDeleteDialog(false);
      
      // Logout and redirect to home page
      await logout();
      router.push('/');

    } catch (error) {
      logger.error('[Account Deletion] Error deleting account:', error);
      toast({
        variant: "destructive",
        title: "Deletion Failed",
        description: error instanceof Error ? error.message : "Failed to delete account. Please try again or contact support.",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleOpenDeleteDialog = () => {
    setStep('warning');
    setConfirmationText('');
    setReason('');
    setShowDeleteDialog(true);
  };

  const handleNextStep = () => {
    if (step === 'warning') {
      setStep('confirmation');
    } else if (step === 'confirmation') {
      setStep('final');
    }
  };

  const handlePreviousStep = () => {
    if (step === 'final') {
      setStep('confirmation');
    } else if (step === 'confirmation') {
      setStep('warning');
    }
  };

  const canProceedFromConfirmation = reason.trim().length > 0;
  const canProceedFromFinal = confirmationText === 'DELETE MY ACCOUNT';

  return (
    <>
      <Card className="border-destructive/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <Trash2 className="h-5 w-5" />
            Delete Account
          </CardTitle>
          <CardDescription>
            Permanently delete your account and all associated data. This action cannot be undone.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Warning:</strong> Account deletion is permanent and irreversible. All your flashcard sets, 
              settings, usage history, and subscription information will be permanently deleted.
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <h4 className="font-medium">What will be deleted:</h4>
            <ul className="text-sm text-muted-foreground space-y-1 ml-4">
              <li>• Your user account and profile information</li>
              <li>• All flashcard sets and generated content</li>
              <li>• Usage history and statistics</li>
              <li>• Subscription information and billing history</li>
              <li>• Settings and preferences</li>
              <li>• Any active subscriptions will be cancelled</li>
            </ul>
          </div>

          <div className="pt-4">
            <Button 
              variant="destructive" 
              onClick={handleOpenDeleteDialog}
              className="w-full sm:w-auto"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete My Account
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Account Deletion Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              {step === 'warning' && 'Delete Account - Warning'}
              {step === 'confirmation' && 'Delete Account - Confirmation'}
              {step === 'final' && 'Delete Account - Final Step'}
            </DialogTitle>
            <DialogDescription>
              {step === 'warning' && 'Please read this important information before proceeding.'}
              {step === 'confirmation' && 'Please tell us why you\'re deleting your account.'}
              {step === 'final' && 'Type the confirmation text to permanently delete your account.'}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {step === 'warning' && (
              <div className="space-y-4">
                <Alert className="border-destructive/20">
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    <strong>This action is permanent and cannot be undone.</strong>
                  </AlertDescription>
                </Alert>

                <div className="space-y-3">
                  <h4 className="font-medium">Before you delete your account:</h4>
                  <ul className="text-sm space-y-2">
                    <li className="flex items-start gap-2">
                      <span className="text-muted-foreground">•</span>
                      <span>Consider exporting your data first using the "Download My Data" feature</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-muted-foreground">•</span>
                      <span>Any active subscriptions will be cancelled immediately</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-muted-foreground">•</span>
                      <span>You will lose access to all your flashcard sets and content</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-muted-foreground">•</span>
                      <span>This action complies with GDPR Article 17 (Right to Erasure)</span>
                    </li>
                  </ul>
                </div>
              </div>
            )}

            {step === 'confirmation' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="deletion-reason">
                    Why are you deleting your account? <span className="text-destructive">*</span>
                  </Label>
                  <Textarea
                    id="deletion-reason"
                    placeholder="Please tell us why you're deleting your account. This helps us improve our service."
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    rows={4}
                  />
                  <p className="text-xs text-muted-foreground">
                    This information is optional but helps us improve our service.
                  </p>
                </div>
              </div>
            )}

            {step === 'final' && (
              <div className="space-y-4">
                <Alert className="border-destructive/20">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Final confirmation required.</strong> This will permanently delete your account and all data.
                  </AlertDescription>
                </Alert>

                <div className="space-y-2">
                  <Label htmlFor="confirmation-text">
                    Type <code className="bg-muted px-1 py-0.5 rounded text-sm">DELETE MY ACCOUNT</code> to confirm:
                  </Label>
                  <Input
                    id="confirmation-text"
                    type="text"
                    placeholder="DELETE MY ACCOUNT"
                    value={confirmationText}
                    onChange={(e) => setConfirmationText(e.target.value)}
                    className={confirmationText === 'DELETE MY ACCOUNT' ? 'border-destructive' : ''}
                  />
                </div>
              </div>
            )}
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <div className="flex gap-2 w-full">
              {step !== 'warning' && (
                <Button 
                  variant="outline" 
                  onClick={handlePreviousStep}
                  className="flex-1 sm:flex-none"
                >
                  Back
                </Button>
              )}
              
              <Button 
                variant="outline" 
                onClick={() => setShowDeleteDialog(false)}
                className="flex-1 sm:flex-none"
              >
                Cancel
              </Button>
              
              {step !== 'final' ? (
                <Button 
                  onClick={handleNextStep}
                  disabled={step === 'confirmation' && !canProceedFromConfirmation}
                  className="flex-1 sm:flex-none"
                >
                  Continue
                </Button>
              ) : (
                <Button 
                  variant="destructive"
                  onClick={handleDeleteAccount}
                  disabled={!canProceedFromFinal || isDeleting}
                  className="flex-1 sm:flex-none"
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Deleting Account...
                    </>
                  ) : (
                    <>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Account Permanently
                    </>
                  )}
                </Button>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
