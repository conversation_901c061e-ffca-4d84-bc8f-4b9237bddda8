/**
 * Content Moderation Service
 * 
 * This service provides automated content scanning and moderation capabilities
 * to detect potentially harmful, inappropriate, or copyrighted content.
 */

import { logger } from './logger';

// Content scanning categories
export type ContentViolationType = 
  | 'SENSITIVE_INFO'      // PII, SSN, credit cards, etc.
  | 'INAPPROPRIATE'       // Offensive language, adult content
  | 'COPYRIGHT'           // Potential copyright infringement
  | 'SPAM'               // Spam or promotional content
  | 'MALICIOUS'          // Malicious links or content
  | 'CLEAN';             // No violations detected

export interface ContentScanResult {
  isViolation: boolean;
  violationType: ContentViolationType;
  confidence: number; // 0-1 confidence score
  details: string;
  suggestedAction: 'ALLOW' | 'WARN' | 'BLOCK' | 'REVIEW';
  flaggedContent?: string[]; // Specific content that was flagged
}

export interface ContentModerationConfig {
  enableSensitiveInfoDetection: boolean;
  enableInappropriateContentDetection: boolean;
  enableCopyrightDetection: boolean;
  enableSpamDetection: boolean;
  strictMode: boolean; // More aggressive filtering
}

// Default configuration
const DEFAULT_CONFIG: ContentModerationConfig = {
  enableSensitiveInfoDetection: true,
  enableInappropriateContentDetection: true,
  enableCopyrightDetection: true,
  enableSpamDetection: true,
  strictMode: false
};

/**
 * Scan text content for potential violations
 */
export async function scanTextContent(
  content: string, 
  config: Partial<ContentModerationConfig> = {}
): Promise<ContentScanResult> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  logger.log('[Content Moderation] Scanning content:', {
    contentLength: content.length,
    config: finalConfig
  });

  try {
    // Run all enabled scans
    const scanResults: ContentScanResult[] = [];

    if (finalConfig.enableSensitiveInfoDetection) {
      scanResults.push(await scanForSensitiveInfo(content));
    }

    if (finalConfig.enableInappropriateContentDetection) {
      scanResults.push(await scanForInappropriateContent(content));
    }

    if (finalConfig.enableCopyrightDetection) {
      scanResults.push(await scanForCopyrightContent(content));
    }

    if (finalConfig.enableSpamDetection) {
      scanResults.push(await scanForSpamContent(content));
    }

    // Determine the most severe violation
    const violations = scanResults.filter(result => result.isViolation);
    
    if (violations.length === 0) {
      return {
        isViolation: false,
        violationType: 'CLEAN',
        confidence: 1.0,
        details: 'No violations detected',
        suggestedAction: 'ALLOW'
      };
    }

    // Return the most severe violation
    const mostSevere = violations.reduce((prev, current) => 
      current.confidence > prev.confidence ? current : prev
    );

    logger.log('[Content Moderation] Violation detected:', {
      type: mostSevere.violationType,
      confidence: mostSevere.confidence,
      action: mostSevere.suggestedAction
    });

    return mostSevere;

  } catch (error) {
    logger.error('[Content Moderation] Error scanning content:', error);
    
    // Return a safe default in case of error
    return {
      isViolation: false,
      violationType: 'CLEAN',
      confidence: 0,
      details: 'Content scanning failed - allowing by default',
      suggestedAction: 'ALLOW'
    };
  }
}

/**
 * Scan for sensitive information (PII, financial data, etc.)
 */
async function scanForSensitiveInfo(content: string): Promise<ContentScanResult> {
  const flaggedContent: string[] = [];
  let maxConfidence = 0;

  // Social Security Numbers (US format)
  const ssnRegex = /\b\d{3}-?\d{2}-?\d{4}\b/g;
  const ssnMatches = content.match(ssnRegex);
  if (ssnMatches) {
    flaggedContent.push(...ssnMatches);
    maxConfidence = Math.max(maxConfidence, 0.9);
  }

  // Credit Card Numbers (basic pattern)
  const ccRegex = /\b(?:\d{4}[-\s]?){3}\d{4}\b/g;
  const ccMatches = content.match(ccRegex);
  if (ccMatches) {
    flaggedContent.push(...ccMatches);
    maxConfidence = Math.max(maxConfidence, 0.8);
  }

  // Email addresses (potential PII)
  const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
  const emailMatches = content.match(emailRegex);
  if (emailMatches && emailMatches.length > 5) { // Only flag if many emails
    flaggedContent.push(...emailMatches.slice(0, 3)); // Show first 3
    maxConfidence = Math.max(maxConfidence, 0.6);
  }

  // Phone numbers
  const phoneRegex = /\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b/g;
  const phoneMatches = content.match(phoneRegex);
  if (phoneMatches && phoneMatches.length > 3) { // Only flag if many phone numbers
    flaggedContent.push(...phoneMatches.slice(0, 2)); // Show first 2
    maxConfidence = Math.max(maxConfidence, 0.5);
  }

  if (flaggedContent.length > 0) {
    return {
      isViolation: true,
      violationType: 'SENSITIVE_INFO',
      confidence: maxConfidence,
      details: `Detected potential sensitive information: ${flaggedContent.length} items`,
      suggestedAction: maxConfidence > 0.7 ? 'BLOCK' : 'WARN',
      flaggedContent
    };
  }

  return {
    isViolation: false,
    violationType: 'CLEAN',
    confidence: 0,
    details: 'No sensitive information detected',
    suggestedAction: 'ALLOW'
  };
}

/**
 * Scan for inappropriate content
 */
async function scanForInappropriateContent(content: string): Promise<ContentScanResult> {
  // Basic inappropriate content detection
  // In production, this would use a more sophisticated service like Google Cloud Natural Language API
  
  const inappropriateKeywords = [
    // Add inappropriate keywords here - keeping this minimal for demo
    'explicit-content-keyword-1',
    'explicit-content-keyword-2'
    // Note: In production, use a proper content moderation API
  ];

  const lowerContent = content.toLowerCase();
  const flaggedWords: string[] = [];

  for (const keyword of inappropriateKeywords) {
    if (lowerContent.includes(keyword.toLowerCase())) {
      flaggedWords.push(keyword);
    }
  }

  if (flaggedWords.length > 0) {
    return {
      isViolation: true,
      violationType: 'INAPPROPRIATE',
      confidence: 0.7,
      details: `Detected potentially inappropriate content: ${flaggedWords.length} flagged terms`,
      suggestedAction: 'REVIEW',
      flaggedContent: flaggedWords
    };
  }

  return {
    isViolation: false,
    violationType: 'CLEAN',
    confidence: 0,
    details: 'No inappropriate content detected',
    suggestedAction: 'ALLOW'
  };
}

/**
 * Scan for potential copyright content
 */
async function scanForCopyrightContent(content: string): Promise<ContentScanResult> {
  const flaggedContent: string[] = [];
  let confidence = 0;

  // Look for copyright notices
  const copyrightRegex = /©|\(c\)|copyright|all rights reserved/gi;
  const copyrightMatches = content.match(copyrightRegex);
  if (copyrightMatches) {
    flaggedContent.push(...copyrightMatches);
    confidence = Math.max(confidence, 0.4);
  }

  // Look for common copyrighted content indicators
  const copyrightIndicators = [
    'proprietary and confidential',
    'trade secret',
    'confidential information',
    'do not distribute',
    'internal use only'
  ];

  for (const indicator of copyrightIndicators) {
    if (content.toLowerCase().includes(indicator)) {
      flaggedContent.push(indicator);
      confidence = Math.max(confidence, 0.6);
    }
  }

  if (flaggedContent.length > 0) {
    return {
      isViolation: true,
      violationType: 'COPYRIGHT',
      confidence,
      details: `Detected potential copyrighted content: ${flaggedContent.length} indicators`,
      suggestedAction: confidence > 0.5 ? 'REVIEW' : 'WARN',
      flaggedContent
    };
  }

  return {
    isViolation: false,
    violationType: 'CLEAN',
    confidence: 0,
    details: 'No copyright indicators detected',
    suggestedAction: 'ALLOW'
  };
}

/**
 * Scan for spam content
 */
async function scanForSpamContent(content: string): Promise<ContentScanResult> {
  const flaggedContent: string[] = [];
  let confidence = 0;

  // Look for spam indicators
  const spamKeywords = [
    'click here now',
    'limited time offer',
    'act now',
    'free money',
    'guaranteed income',
    'work from home',
    'make money fast'
  ];

  const lowerContent = content.toLowerCase();
  for (const keyword of spamKeywords) {
    if (lowerContent.includes(keyword)) {
      flaggedContent.push(keyword);
      confidence = Math.max(confidence, 0.3);
    }
  }

  // Look for excessive URLs
  const urlRegex = /https?:\/\/[^\s]+/gi;
  const urlMatches = content.match(urlRegex);
  if (urlMatches && urlMatches.length > 5) {
    flaggedContent.push(`${urlMatches.length} URLs detected`);
    confidence = Math.max(confidence, 0.5);
  }

  if (flaggedContent.length > 0) {
    return {
      isViolation: true,
      violationType: 'SPAM',
      confidence,
      details: `Detected potential spam content: ${flaggedContent.length} indicators`,
      suggestedAction: confidence > 0.4 ? 'REVIEW' : 'WARN',
      flaggedContent
    };
  }

  return {
    isViolation: false,
    violationType: 'CLEAN',
    confidence: 0,
    details: 'No spam indicators detected',
    suggestedAction: 'ALLOW'
  };
}
