import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DMCATakedownForm } from '@/components/DMCATakedownForm';

export const metadata: Metadata = {
  title: 'DMCA Takedown Request - Flash Cards AI',
  description: 'Submit a DMCA takedown notice for copyrighted content on Flash Cards AI.',
  robots: 'noindex, nofollow'
};

export default function DMCAPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Navigation */}
        <div className="mb-6">
          <Button variant="ghost" asChild>
            <Link href="/legal" className="flex items-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Legal
            </Link>
          </Button>
        </div>

        {/* DMCA Policy Information */}
        <div className="mb-8 prose prose-gray max-w-none">
          <h1>DMCA Takedown Policy</h1>
          
          <p>
            Flash Cards AI respects the intellectual property rights of others and expects our users to do the same. 
            In accordance with the Digital Millennium Copyright Act (DMCA), we will respond to valid takedown notices 
            and remove infringing content when properly notified.
          </p>

          <h2>Our Process</h2>
          <ol>
            <li>Submit a complete DMCA takedown notice using the form below</li>
            <li>We review the notice within 2-3 business days</li>
            <li>If valid, we remove or disable access to the infringing content</li>
            <li>We notify the content creator of the takedown</li>
            <li>The content creator may submit a counter-notice if they believe the takedown was invalid</li>
          </ol>

          <h2>Requirements for a Valid DMCA Notice</h2>
          <p>Your takedown notice must include:</p>
          <ul>
            <li>Identification of the copyrighted work claimed to be infringed</li>
            <li>Identification of the infringing material and its location on our platform</li>
            <li>Your contact information (name, address, phone, email)</li>
            <li>A statement that you have a good faith belief that the use is not authorized</li>
            <li>A statement that the information is accurate and you are authorized to act on behalf of the copyright owner</li>
            <li>Your physical or electronic signature</li>
          </ul>

          <h2>Counter-Notice Process</h2>
          <p>
            If you believe your content was removed in error, you may submit a counter-notice. 
            We will restore the content within 10-14 business days unless the copyright owner 
            files a court action seeking to restrain the allegedly infringing activity.
          </p>

          <h2>Repeat Infringer Policy</h2>
          <p>
            We maintain a policy of terminating accounts of users who are repeat infringers 
            of copyrighted material when appropriate and in our sole discretion.
          </p>

          <h2>Contact Information</h2>
          <p>
            <strong>DMCA Agent:</strong><br />
            Anker Studios<br />
            Email: <EMAIL><br />
            Address: South Africa
          </p>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 my-6">
            <p className="text-yellow-800 font-medium mb-2">⚠️ Important Legal Notice</p>
            <p className="text-yellow-700 text-sm">
              Submitting false or misleading DMCA claims may result in legal consequences under Section 512(f) 
              of the DMCA, which provides for damages and attorney fees against any person who knowingly 
              materially misrepresents that content is infringing.
            </p>
          </div>
        </div>

        {/* DMCA Takedown Form */}
        <DMCATakedownForm />
      </div>
    </div>
  );
}
