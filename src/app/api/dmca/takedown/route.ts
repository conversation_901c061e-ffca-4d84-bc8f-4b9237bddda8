import { NextRequest, NextResponse } from 'next/server';
import { isFirebaseAdminConfigured, adminDb } from '@/lib/firebase-admin';
import { logger } from '@/lib/logger';

interface DMCATakedownRequest {
  // Complainant Information
  complainantName: string;
  complainantEmail: string;
  complainantAddress: string;
  complainantPhone?: string;
  
  // Copyright Information
  copyrightWork: string;
  copyrightOwner: string;
  originalWorkUrl?: string;
  
  // Infringing Content
  infringingContentUrl: string;
  infringingContentDescription: string;
  flashcardSetId?: string;
  
  // Legal Statements
  goodFaithBelief: boolean;
  accuracyStatement: boolean;
  authorityStatement: boolean;
  
  // Additional Information
  additionalInfo?: string;
}

interface DMCATakedownResponse {
  success: boolean;
  message?: string;
  error?: string;
  ticketId?: string;
}

/**
 * POST /api/dmca/takedown - Submit DMCA takedown request
 * 
 * This endpoint handles DMCA takedown notices according to the Digital Millennium Copyright Act.
 * It creates a takedown request record and initiates the review process.
 */
export async function POST(request: NextRequest): Promise<NextResponse<DMCATakedownResponse>> {
  try {
    // Check if Firebase Admin is properly configured
    if (!isFirebaseAdminConfigured()) {
      logger.error('[DMCA Takedown] Firebase Admin not configured');
      return NextResponse.json(
        {
          success: false,
          error: 'DMCA takedown service is currently unavailable. Please contact support.',
        },
        { status: 503 }
      );
    }

    const body: DMCATakedownRequest = await request.json();
    
    // Validate required fields
    const requiredFields = [
      'complainantName',
      'complainantEmail', 
      'complainantAddress',
      'copyrightWork',
      'copyrightOwner',
      'infringingContentUrl',
      'infringingContentDescription',
      'goodFaithBelief',
      'accuracyStatement',
      'authorityStatement'
    ];

    for (const field of requiredFields) {
      if (!body[field as keyof DMCATakedownRequest]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.complainantEmail)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email address format' },
        { status: 400 }
      );
    }

    // Validate legal statements
    if (!body.goodFaithBelief || !body.accuracyStatement || !body.authorityStatement) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'All legal statements must be acknowledged to proceed with DMCA takedown request' 
        },
        { status: 400 }
      );
    }

    // Generate unique ticket ID
    const ticketId = `DMCA-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    logger.log('[DMCA Takedown] Processing takedown request:', {
      ticketId,
      complainantEmail: body.complainantEmail,
      infringingContentUrl: body.infringingContentUrl
    });

    // Create DMCA takedown record
    const takedownRecord = {
      ticketId,
      
      // Complainant Information
      complainant: {
        name: body.complainantName,
        email: body.complainantEmail,
        address: body.complainantAddress,
        phone: body.complainantPhone || null
      },
      
      // Copyright Information
      copyright: {
        work: body.copyrightWork,
        owner: body.copyrightOwner,
        originalWorkUrl: body.originalWorkUrl || null
      },
      
      // Infringing Content
      infringingContent: {
        url: body.infringingContentUrl,
        description: body.infringingContentDescription,
        flashcardSetId: body.flashcardSetId || null
      },
      
      // Legal Statements
      legalStatements: {
        goodFaithBelief: body.goodFaithBelief,
        accuracyStatement: body.accuracyStatement,
        authorityStatement: body.authorityStatement
      },
      
      // Additional Information
      additionalInfo: body.additionalInfo || null,
      
      // Status and Timestamps
      status: 'SUBMITTED',
      submittedAt: new Date().toISOString(),
      reviewedAt: null,
      resolvedAt: null,
      
      // Processing Information
      reviewerNotes: null,
      actionTaken: null,
      
      // Metadata
      ipAddress: request.headers.get('x-forwarded-for') || 
                 request.headers.get('x-real-ip') || 
                 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    };

    // Save to Firestore
    await adminDb.collection('dmcaTakedowns').doc(ticketId).set(takedownRecord);
    
    logger.log('[DMCA Takedown] Takedown request saved:', ticketId);

    // If flashcard set ID is provided, immediately flag the content for review
    if (body.flashcardSetId) {
      try {
        await flagFlashcardSetForReview(body.flashcardSetId, ticketId);
        logger.log('[DMCA Takedown] Flashcard set flagged for review:', body.flashcardSetId);
      } catch (error) {
        logger.error('[DMCA Takedown] Error flagging flashcard set:', error);
        // Don't fail the takedown request if flagging fails
      }
    }

    // TODO: Send notification email to admin team
    // TODO: Send acknowledgment email to complainant

    return NextResponse.json({
      success: true,
      message: 'DMCA takedown request submitted successfully. You will receive an email confirmation shortly.',
      ticketId
    });

  } catch (error) {
    logger.error('[DMCA Takedown] Error processing takedown request:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process DMCA takedown request. Please try again or contact support.' 
      },
      { status: 500 }
    );
  }
}

/**
 * Flag a flashcard set for review due to DMCA complaint
 */
async function flagFlashcardSetForReview(flashcardSetId: string, dmcaTicketId: string): Promise<void> {
  try {
    const flashcardSetRef = adminDb.collection('flashcardSets').doc(flashcardSetId);
    const flashcardSetDoc = await flashcardSetRef.get();
    
    if (!flashcardSetDoc.exists) {
      logger.warn('[DMCA Takedown] Flashcard set not found:', flashcardSetId);
      return;
    }

    // Update flashcard set with DMCA flag
    await flashcardSetRef.update({
      'moderation.dmcaFlagged': true,
      'moderation.dmcaTicketId': dmcaTicketId,
      'moderation.dmcaFlaggedAt': new Date().toISOString(),
      'moderation.status': 'UNDER_REVIEW',
      'moderation.reviewReason': 'DMCA_COMPLAINT'
    });

    logger.log('[DMCA Takedown] Flashcard set flagged:', {
      flashcardSetId,
      dmcaTicketId
    });

  } catch (error) {
    logger.error('[DMCA Takedown] Error flagging flashcard set:', error);
    throw error;
  }
}

/**
 * GET /api/dmca/takedown - Get DMCA takedown status (for complainants)
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const ticketId = searchParams.get('ticketId');
    const email = searchParams.get('email');

    if (!ticketId || !email) {
      return NextResponse.json(
        { success: false, error: 'Ticket ID and email are required' },
        { status: 400 }
      );
    }

    // Check if Firebase Admin is properly configured
    if (!isFirebaseAdminConfigured()) {
      return NextResponse.json(
        { success: false, error: 'Service temporarily unavailable' },
        { status: 503 }
      );
    }

    // Get takedown record
    const takedownDoc = await adminDb.collection('dmcaTakedowns').doc(ticketId).get();
    
    if (!takedownDoc.exists) {
      return NextResponse.json(
        { success: false, error: 'DMCA takedown request not found' },
        { status: 404 }
      );
    }

    const takedownData = takedownDoc.data();
    
    // Verify email matches (security check)
    if (takedownData?.complainant?.email !== email) {
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 403 }
      );
    }

    // Return public status information
    return NextResponse.json({
      success: true,
      takedown: {
        ticketId: takedownData.ticketId,
        status: takedownData.status,
        submittedAt: takedownData.submittedAt,
        reviewedAt: takedownData.reviewedAt,
        resolvedAt: takedownData.resolvedAt,
        actionTaken: takedownData.actionTaken
      }
    });

  } catch (error) {
    logger.error('[DMCA Takedown] Error getting takedown status:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get takedown status' },
      { status: 500 }
    );
  }
}
