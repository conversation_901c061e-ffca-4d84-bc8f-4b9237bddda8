import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '@/lib/firebase-admin';
import { logger } from '@/lib/logger';

export interface DMCATakedownRequest {
  // Complainant information
  complainantName: string;
  complainantEmail: string;
  complainantAddress: string;
  complainantPhone?: string;
  
  // Copyright information
  copyrightWork: string;
  copyrightOwner: string;
  originalWorkUrl?: string;
  
  // Infringing content information
  infringingContentUrl: string;
  infringingContentDescription: string;
  flashcardSetId?: string;
  
  // Legal declarations
  goodFaithBelief: boolean;
  accuracyStatement: boolean;
  authorizedToAct: boolean;
  
  // Additional information
  additionalInfo?: string;
  submittedAt: Date;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = [
      'complainantName',
      'complainantEmail', 
      'complainantAddress',
      'copyrightWork',
      'copyrightOwner',
      'infringingContentUrl',
      'infringingContentDescription',
      'goodFaithBelief',
      'accuracyStatement',
      'authorizedToAct'
    ];

    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate boolean fields
    if (typeof body.goodFaithBelief !== 'boolean' || 
        typeof body.accuracyStatement !== 'boolean' || 
        typeof body.authorizedToAct !== 'boolean') {
      return NextResponse.json(
        { success: false, error: 'Legal declarations must be explicitly confirmed' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.complainantEmail)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email address format' },
        { status: 400 }
      );
    }

    // Create DMCA takedown request
    const takedownRequest: DMCATakedownRequest = {
      complainantName: body.complainantName.trim(),
      complainantEmail: body.complainantEmail.trim().toLowerCase(),
      complainantAddress: body.complainantAddress.trim(),
      complainantPhone: body.complainantPhone?.trim(),
      copyrightWork: body.copyrightWork.trim(),
      copyrightOwner: body.copyrightOwner.trim(),
      originalWorkUrl: body.originalWorkUrl?.trim(),
      infringingContentUrl: body.infringingContentUrl.trim(),
      infringingContentDescription: body.infringingContentDescription.trim(),
      flashcardSetId: body.flashcardSetId?.trim(),
      goodFaithBelief: body.goodFaithBelief,
      accuracyStatement: body.accuracyStatement,
      authorizedToAct: body.authorizedToAct,
      additionalInfo: body.additionalInfo?.trim(),
      submittedAt: new Date()
    };

    // Store the takedown request in Firestore
    const docRef = await adminDb.collection('dmcaTakedownRequests').add({
      ...takedownRequest,
      status: 'pending',
      reviewedAt: null,
      reviewedBy: null,
      resolution: null,
      resolutionNotes: null
    });

    logger.log(`[DMCA] New takedown request submitted: ${docRef.id}`, {
      complainantEmail: takedownRequest.complainantEmail,
      copyrightWork: takedownRequest.copyrightWork,
      infringingContentUrl: takedownRequest.infringingContentUrl
    });

    // If a specific flashcard set is mentioned, flag it for review
    if (takedownRequest.flashcardSetId) {
      try {
        const flashcardRef = adminDb.collection('flashcardSets').doc(takedownRequest.flashcardSetId);
        const flashcardDoc = await flashcardRef.get();
        
        if (flashcardDoc.exists) {
          await flashcardRef.update({
            dmcaFlagged: true,
            dmcaFlaggedAt: new Date(),
            dmcaRequestId: docRef.id,
            status: 'under_review'
          });
          
          logger.log(`[DMCA] Flagged flashcard set ${takedownRequest.flashcardSetId} for review`);
        }
      } catch (error) {
        logger.error(`[DMCA] Error flagging flashcard set ${takedownRequest.flashcardSetId}:`, error);
        // Continue processing even if flagging fails
      }
    }

    // Send confirmation response
    return NextResponse.json({
      success: true,
      message: 'DMCA takedown request submitted successfully',
      requestId: docRef.id,
      submittedAt: takedownRequest.submittedAt.toISOString()
    });

  } catch (error) {
    logger.error('[DMCA] Error processing takedown request:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error while processing DMCA request' },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve DMCA policy information
export async function GET() {
  const dmcaPolicy = {
    title: 'DMCA Takedown Policy',
    description: 'Flash Cards AI respects intellectual property rights and responds to valid DMCA takedown notices.',
    process: [
      'Submit a complete DMCA takedown notice using our form',
      'We review the notice within 2-3 business days',
      'If valid, we remove or disable access to the infringing content',
      'We notify the content creator of the takedown',
      'The content creator may submit a counter-notice if they believe the takedown was invalid'
    ],
    requirements: [
      'Identification of the copyrighted work claimed to be infringed',
      'Identification of the infringing material and its location',
      'Contact information of the complainant',
      'A statement of good faith belief that the use is not authorized',
      'A statement that the information is accurate and the complainant is authorized to act',
      'Physical or electronic signature of the copyright owner or authorized agent'
    ],
    contactInfo: {
      email: '<EMAIL>',
      address: 'Anker Studios, DMCA Agent, South Africa'
    }
  };

  return NextResponse.json(dmcaPolicy);
}
