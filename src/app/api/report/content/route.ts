import { NextRequest, NextResponse } from 'next/server';
import { isFirebaseAdminConfigured, adminAuth, adminDb } from '@/lib/firebase-admin';
import { logger } from '@/lib/logger';

export type ReportReason = 
  | 'INAPPROPRIATE_CONTENT'
  | 'COPYRIGHT_INFRINGEMENT' 
  | 'SPAM'
  | 'HARASSMENT'
  | 'MISINFORMATION'
  | 'PRIVACY_VIOLATION'
  | 'OTHER';

interface ContentReportRequest {
  // Reporter Information (optional for anonymous reports)
  reporterUserId?: string;
  reporterEmail?: string;
  
  // Content Information
  contentType: 'FLASHCARD_SET' | 'USER_PROFILE' | 'OTHER';
  contentId: string;
  contentUrl?: string;
  
  // Report Details
  reason: ReportReason;
  description: string;
  
  // Additional Context
  additionalInfo?: string;
  isAnonymous?: boolean;
}

interface ContentReportResponse {
  success: boolean;
  message?: string;
  error?: string;
  reportId?: string;
}

/**
 * POST /api/report/content - Submit content report
 * 
 * This endpoint allows users to report inappropriate content, copyright violations,
 * spam, or other policy violations.
 */
export async function POST(request: NextRequest): Promise<NextResponse<ContentReportResponse>> {
  try {
    // Check if Firebase Admin is properly configured
    if (!isFirebaseAdminConfigured()) {
      logger.error('[Content Report] Firebase Admin not configured');
      return NextResponse.json(
        {
          success: false,
          error: 'Content reporting service is currently unavailable. Please contact support.',
        },
        { status: 503 }
      );
    }

    const body: ContentReportRequest = await request.json();
    
    // Validate required fields
    if (!body.contentType || !body.contentId || !body.reason || !body.description) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: contentType, contentId, reason, description' },
        { status: 400 }
      );
    }

    // Validate reason
    const validReasons: ReportReason[] = [
      'INAPPROPRIATE_CONTENT',
      'COPYRIGHT_INFRINGEMENT',
      'SPAM', 
      'HARASSMENT',
      'MISINFORMATION',
      'PRIVACY_VIOLATION',
      'OTHER'
    ];
    
    if (!validReasons.includes(body.reason)) {
      return NextResponse.json(
        { success: false, error: 'Invalid report reason' },
        { status: 400 }
      );
    }

    // Validate content type
    const validContentTypes = ['FLASHCARD_SET', 'USER_PROFILE', 'OTHER'];
    if (!validContentTypes.includes(body.contentType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid content type' },
        { status: 400 }
      );
    }

    // Validate description length
    if (body.description.length < 10 || body.description.length > 1000) {
      return NextResponse.json(
        { success: false, error: 'Description must be between 10 and 1000 characters' },
        { status: 400 }
      );
    }

    // Validate reporter information if provided
    let reporterInfo = null;
    if (body.reporterUserId) {
      try {
        const userRecord = await adminAuth.getUser(body.reporterUserId);
        reporterInfo = {
          userId: body.reporterUserId,
          email: userRecord.email || null,
          displayName: userRecord.displayName || null
        };
      } catch (error) {
        logger.error('[Content Report] Invalid reporter user ID:', error);
        return NextResponse.json(
          { success: false, error: 'Invalid reporter user ID' },
          { status: 400 }
        );
      }
    } else if (body.reporterEmail && !body.isAnonymous) {
      // Validate email format for non-anonymous reports
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(body.reporterEmail)) {
        return NextResponse.json(
          { success: false, error: 'Invalid email address format' },
          { status: 400 }
        );
      }
      reporterInfo = {
        email: body.reporterEmail
      };
    }

    // Generate unique report ID
    const reportId = `RPT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    logger.log('[Content Report] Processing content report:', {
      reportId,
      contentType: body.contentType,
      contentId: body.contentId,
      reason: body.reason,
      isAnonymous: body.isAnonymous || false
    });

    // Create content report record
    const reportRecord = {
      reportId,
      
      // Reporter Information
      reporter: reporterInfo,
      isAnonymous: body.isAnonymous || false,
      
      // Content Information
      content: {
        type: body.contentType,
        id: body.contentId,
        url: body.contentUrl || null
      },
      
      // Report Details
      reason: body.reason,
      description: body.description,
      additionalInfo: body.additionalInfo || null,
      
      // Status and Timestamps
      status: 'SUBMITTED',
      submittedAt: new Date().toISOString(),
      reviewedAt: null,
      resolvedAt: null,
      
      // Processing Information
      reviewerNotes: null,
      actionTaken: null,
      moderatorUserId: null,
      
      // Metadata
      ipAddress: request.headers.get('x-forwarded-for') || 
                 request.headers.get('x-real-ip') || 
                 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    };

    // Save to Firestore
    await adminDb.collection('contentReports').doc(reportId).set(reportRecord);
    
    logger.log('[Content Report] Content report saved:', reportId);

    // Flag the reported content for review
    try {
      await flagContentForReview(body.contentType, body.contentId, reportId, body.reason);
      logger.log('[Content Report] Content flagged for review:', body.contentId);
    } catch (error) {
      logger.error('[Content Report] Error flagging content:', error);
      // Don't fail the report if flagging fails
    }

    // TODO: Send notification to moderation team
    // TODO: Send acknowledgment email to reporter (if not anonymous)

    return NextResponse.json({
      success: true,
      message: 'Content report submitted successfully. Our moderation team will review it shortly.',
      reportId
    });

  } catch (error) {
    logger.error('[Content Report] Error processing content report:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process content report. Please try again or contact support.' 
      },
      { status: 500 }
    );
  }
}

/**
 * Flag content for moderation review
 */
async function flagContentForReview(
  contentType: string, 
  contentId: string, 
  reportId: string, 
  reason: ReportReason
): Promise<void> {
  try {
    if (contentType === 'FLASHCARD_SET') {
      const flashcardSetRef = adminDb.collection('flashcardSets').doc(contentId);
      const flashcardSetDoc = await flashcardSetRef.get();
      
      if (!flashcardSetDoc.exists) {
        logger.warn('[Content Report] Flashcard set not found:', contentId);
        return;
      }

      // Update flashcard set with report flag
      await flashcardSetRef.update({
        'moderation.reported': true,
        'moderation.reportId': reportId,
        'moderation.reportedAt': new Date().toISOString(),
        'moderation.reportReason': reason,
        'moderation.status': 'UNDER_REVIEW',
        'moderation.reviewReason': 'USER_REPORT'
      });

    } else if (contentType === 'USER_PROFILE') {
      // Flag user profile for review
      const userRef = adminDb.collection('users').doc(contentId);
      const userDoc = await userRef.get();
      
      if (!userDoc.exists) {
        logger.warn('[Content Report] User profile not found:', contentId);
        return;
      }

      await userRef.update({
        'moderation.reported': true,
        'moderation.reportId': reportId,
        'moderation.reportedAt': new Date().toISOString(),
        'moderation.reportReason': reason,
        'moderation.status': 'UNDER_REVIEW'
      });
    }

    logger.log('[Content Report] Content flagged for review:', {
      contentType,
      contentId,
      reportId,
      reason
    });

  } catch (error) {
    logger.error('[Content Report] Error flagging content for review:', error);
    throw error;
  }
}

/**
 * GET /api/report/content - Get report status (for reporters)
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const reportId = searchParams.get('reportId');
    const email = searchParams.get('email');

    if (!reportId) {
      return NextResponse.json(
        { success: false, error: 'Report ID is required' },
        { status: 400 }
      );
    }

    // Check if Firebase Admin is properly configured
    if (!isFirebaseAdminConfigured()) {
      return NextResponse.json(
        { success: false, error: 'Service temporarily unavailable' },
        { status: 503 }
      );
    }

    // Get report record
    const reportDoc = await adminDb.collection('contentReports').doc(reportId).get();
    
    if (!reportDoc.exists) {
      return NextResponse.json(
        { success: false, error: 'Content report not found' },
        { status: 404 }
      );
    }

    const reportData = reportDoc.data();
    
    // For anonymous reports, no email verification needed
    if (!reportData?.isAnonymous && email) {
      // Verify email matches (security check)
      if (reportData?.reporter?.email !== email) {
        return NextResponse.json(
          { success: false, error: 'Invalid credentials' },
          { status: 403 }
        );
      }
    }

    // Return public status information
    return NextResponse.json({
      success: true,
      report: {
        reportId: reportData.reportId,
        status: reportData.status,
        reason: reportData.reason,
        submittedAt: reportData.submittedAt,
        reviewedAt: reportData.reviewedAt,
        resolvedAt: reportData.resolvedAt,
        actionTaken: reportData.actionTaken
      }
    });

  } catch (error) {
    logger.error('[Content Report] Error getting report status:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get report status' },
      { status: 500 }
    );
  }
}
