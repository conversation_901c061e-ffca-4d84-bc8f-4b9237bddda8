import { NextRequest, NextResponse } from 'next/server';
import { adminDb, isFirebaseAdminConfigured } from '@/lib/firebase-admin';
import { logger } from '@/lib/logger';

interface RefundRequest {
  userId: string;
  userEmail: string;
  subscriptionId?: string;
  reason: string;
  description: string;
  requestedAmount?: number;
  paymentReference?: string;
  createdAt: string;
  status: 'pending' | 'approved' | 'denied' | 'processed';
  adminNotes?: string;
  processedAt?: string;
  processedBy?: string;
}

interface RefundRequestInput {
  userId: string;
  reason: 'service_unavailable' | 'billing_error' | 'early_cancellation' | 'technical_issues' | 'other';
  description: string;
  paymentReference?: string;
}

// Rate limiting for refund requests
const refundRequestTracker = new Map<string, { count: number; lastRequest: number }>();
const RATE_LIMIT_WINDOW = 24 * 60 * 60 * 1000; // 24 hours
const MAX_REFUND_REQUESTS_PER_DAY = 3;

function checkRateLimit(userId: string): { allowed: boolean; resetTime?: number } {
  const now = Date.now();
  const userRequests = refundRequestTracker.get(userId);

  if (!userRequests) {
    refundRequestTracker.set(userId, { count: 1, lastRequest: now });
    return { allowed: true };
  }

  // Reset if window has passed
  if (now - userRequests.lastRequest > RATE_LIMIT_WINDOW) {
    refundRequestTracker.set(userId, { count: 1, lastRequest: now });
    return { allowed: true };
  }

  // Check if limit exceeded
  if (userRequests.count >= MAX_REFUND_REQUESTS_PER_DAY) {
    const resetTime = userRequests.lastRequest + RATE_LIMIT_WINDOW;
    return { allowed: false, resetTime };
  }

  // Increment count
  userRequests.count++;
  userRequests.lastRequest = now;
  refundRequestTracker.set(userId, userRequests);
  
  return { allowed: true };
}

/**
 * POST /api/user/refund-request - Submit a refund request
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check if Firebase Admin is properly configured
    if (!isFirebaseAdminConfigured()) {
      logger.error('[Refund Request] Firebase Admin not configured - missing service account credentials');
      return NextResponse.json(
        {
          success: false,
          error: 'Refund request service is currently unavailable. Please contact support.',
          code: 'SERVICE_UNAVAILABLE'
        },
        { status: 503 }
      );
    }

    const body: RefundRequestInput = await request.json();
    const { userId, reason, description, paymentReference } = body;

    // Validate required fields
    if (!userId || !reason || !description) {
      return NextResponse.json(
        { success: false, error: 'User ID, reason, and description are required' },
        { status: 400 }
      );
    }

    // Validate reason
    const validReasons = ['service_unavailable', 'billing_error', 'early_cancellation', 'technical_issues', 'other'];
    if (!validReasons.includes(reason)) {
      return NextResponse.json(
        { success: false, error: 'Invalid refund reason' },
        { status: 400 }
      );
    }

    // Check rate limiting
    const rateLimitCheck = checkRateLimit(userId);
    if (!rateLimitCheck.allowed) {
      const resetTime = new Date(rateLimitCheck.resetTime!).toISOString();
      logger.warn('[Refund Request] Rate limit exceeded for user:', userId);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Too many refund requests. Please wait 24 hours before submitting another request.',
          resetTime 
        },
        { status: 429 }
      );
    }

    logger.log('[Refund Request] Processing refund request for user:', userId);

    // Get user information
    let userEmail = '';
    try {
      const userDoc = await adminDb.collection('users').doc(userId).get();
      if (userDoc.exists) {
        userEmail = userDoc.data()?.email || '';
      }
    } catch (error) {
      logger.error('[Refund Request] Error fetching user data:', error);
    }

    // Get subscription information
    let subscriptionId = '';
    let requestedAmount = 0;
    try {
      const subscriptionSnapshot = await adminDb
        .collection('subscriptions')
        .where('userId', '==', userId)
        .where('status', '==', 'active')
        .limit(1)
        .get();

      if (!subscriptionSnapshot.empty) {
        const subscriptionData = subscriptionSnapshot.docs[0].data();
        subscriptionId = subscriptionSnapshot.docs[0].id;
        
        // Estimate refund amount based on subscription tier
        const tier = subscriptionData.tier;
        if (tier === 'BASIC') {
          requestedAmount = 29; // R29 for Basic
        } else if (tier === 'PRO') {
          requestedAmount = 75; // R75 for Pro
        }
      }
    } catch (error) {
      logger.error('[Refund Request] Error fetching subscription data:', error);
    }

    // Create refund request document
    const refundRequest: RefundRequest = {
      userId,
      userEmail,
      subscriptionId,
      reason,
      description: description.trim(),
      requestedAmount,
      paymentReference: paymentReference?.trim() || '',
      createdAt: new Date().toISOString(),
      status: 'pending'
    };

    // Save refund request to Firestore
    const refundRequestRef = await adminDb.collection('refundRequests').add(refundRequest);

    // Log the request for admin notification
    logger.log('[Refund Request] New refund request created:', {
      requestId: refundRequestRef.id,
      userId,
      userEmail,
      reason,
      requestedAmount,
      subscriptionId
    });

    // TODO: Send email notification to admin team
    // This would typically integrate with an email service like SendGrid or similar

    return NextResponse.json({
      success: true,
      requestId: refundRequestRef.id,
      message: 'Refund request submitted successfully. We will review your request within 5 business days and contact you via email.',
      estimatedReviewTime: '5 business days'
    });

  } catch (error) {
    logger.error('[Refund Request] Error processing refund request:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit refund request. Please try again.' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/user/refund-request - Get user's refund requests
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check if Firebase Admin is properly configured
    if (!isFirebaseAdminConfigured()) {
      logger.error('[Refund Request] Firebase Admin not configured - missing service account credentials');
      return NextResponse.json(
        {
          success: false,
          error: 'Refund request service is currently unavailable. Please contact support.',
          code: 'SERVICE_UNAVAILABLE'
        },
        { status: 503 }
      );
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get user's refund requests
    const refundRequestsSnapshot = await adminDb
      .collection('refundRequests')
      .where('userId', '==', userId)
      .orderBy('createdAt', 'desc')
      .limit(10)
      .get();

    const refundRequests = refundRequestsSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        reason: data.reason,
        description: data.description,
        requestedAmount: data.requestedAmount,
        status: data.status,
        createdAt: data.createdAt,
        processedAt: data.processedAt,
        adminNotes: data.adminNotes
      };
    });

    return NextResponse.json({
      success: true,
      refundRequests
    });

  } catch (error) {
    logger.error('[Refund Request] Error fetching refund requests:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch refund requests' },
      { status: 500 }
    );
  }
}
