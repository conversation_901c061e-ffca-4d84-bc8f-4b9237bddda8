import { NextRequest, NextResponse } from 'next/server';
import { isFirebaseAdminConfigured, adminAuth, adminDb } from '@/lib/firebase-admin';
import { logger } from '@/lib/logger';
import axios from 'axios';

interface DeleteAccountRequest {
  userId: string;
  confirmationText: string;
  reason?: string;
}

interface DeleteAccountResponse {
  success: boolean;
  message?: string;
  error?: string;
  code?: string;
}

/**
 * POST /api/user/delete-account - Delete user account and all associated data
 * 
 * This endpoint implements GDPR Article 17 (Right to Erasure) by:
 * 1. Cancelling active subscriptions
 * 2. Deleting all user data from Firestore collections
 * 3. Deleting the Firebase Auth user account
 * 4. Logging the deletion for compliance records
 */
export async function POST(request: NextRequest): Promise<NextResponse<DeleteAccountResponse>> {
  try {
    // Check if Firebase Admin is properly configured
    if (!isFirebaseAdminConfigured()) {
      logger.error('[Account Deletion] Firebase Admin not configured - missing service account credentials');
      return NextResponse.json(
        {
          success: false,
          error: 'Account deletion service is currently unavailable. Please contact support.',
          code: 'SERVICE_UNAVAILABLE'
        },
        { status: 503 }
      );
    }

    const body: DeleteAccountRequest = await request.json();
    const { userId, confirmationText, reason } = body;

    // Validate required fields
    if (!userId || typeof userId !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Valid user ID is required' },
        { status: 400 }
      );
    }

    if (!confirmationText || confirmationText.toLowerCase() !== 'delete my account') {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Please type "DELETE MY ACCOUNT" exactly to confirm account deletion' 
        },
        { status: 400 }
      );
    }

    logger.log('[Account Deletion] Starting account deletion process for user:', userId);

    // Get user information before deletion for logging and email
    let userEmail: string | null = null;
    let userDisplayName: string | null = null;
    
    try {
      const userRecord = await adminAuth.getUser(userId);
      userEmail = userRecord.email || null;
      userDisplayName = userRecord.displayName || null;
      logger.log('[Account Deletion] User info retrieved:', { 
        email: userEmail, 
        displayName: userDisplayName 
      });
    } catch (error) {
      logger.error('[Account Deletion] Error getting user info:', error);
      // Continue with deletion even if we can't get user info
    }

    // Step 1: Cancel active subscriptions
    if (userEmail) {
      try {
        await cancelUserSubscriptions(userEmail);
        logger.log('[Account Deletion] Subscriptions cancelled for user:', userId);
      } catch (error) {
        logger.error('[Account Deletion] Error cancelling subscriptions:', error);
        // Continue with deletion even if subscription cancellation fails
      }
    }

    // Step 2: Delete all user data from Firestore collections
    const deletionResults = await deleteUserDataFromFirestore(userId);
    logger.log('[Account Deletion] Firestore data deletion results:', deletionResults);

    // Step 3: Delete Firebase Auth user account
    try {
      await adminAuth.deleteUser(userId);
      logger.log('[Account Deletion] Firebase Auth user deleted:', userId);
    } catch (error) {
      logger.error('[Account Deletion] Error deleting Firebase Auth user:', error);
      // This is critical - if we can't delete the auth user, the account deletion failed
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to delete user account. Please contact support.' 
        },
        { status: 500 }
      );
    }

    // Step 4: Log the deletion for compliance records
    const deletionRecord = {
      userId,
      userEmail,
      userDisplayName,
      reason: reason || 'User requested account deletion',
      deletedAt: new Date().toISOString(),
      deletionResults,
      requestedBy: 'user'
    };

    try {
      // Store deletion record in a separate collection for compliance
      await adminDb.collection('accountDeletions').add(deletionRecord);
      logger.log('[Account Deletion] Deletion record created for compliance');
    } catch (error) {
      logger.error('[Account Deletion] Error creating deletion record:', error);
      // Don't fail the deletion if we can't create the record
    }

    // Step 5: Send confirmation email (if email service is available)
    if (userEmail) {
      try {
        await sendAccountDeletionConfirmation(userEmail, userDisplayName);
        logger.log('[Account Deletion] Confirmation email sent to:', userEmail);
      } catch (error) {
        logger.error('[Account Deletion] Error sending confirmation email:', error);
        // Don't fail the deletion if we can't send email
      }
    }

    logger.log('[Account Deletion] Account deletion completed successfully for user:', userId);

    return NextResponse.json({
      success: true,
      message: 'Your account and all associated data have been permanently deleted. A confirmation email has been sent to your registered email address.'
    });

  } catch (error) {
    logger.error('[Account Deletion] Unexpected error during account deletion:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred during account deletion. Please contact support.' 
      },
      { status: 500 }
    );
  }
}

/**
 * Cancel all active subscriptions for a user
 */
async function cancelUserSubscriptions(userEmail: string): Promise<void> {
  try {
    // Get user's active subscriptions
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/paystack/subscription/${encodeURIComponent(userEmail)}`);
    
    if (!response.ok) {
      logger.log('[Account Deletion] No subscriptions found or error fetching subscriptions');
      return;
    }

    const subscriptionData = await response.json();
    
    if (!subscriptionData.status || !subscriptionData.data || subscriptionData.data.length === 0) {
      logger.log('[Account Deletion] No active subscriptions to cancel');
      return;
    }

    // Cancel each active subscription
    for (const subscription of subscriptionData.data) {
      if (subscription.status === 'active') {
        try {
          const cancelResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/paystack/cancel-subscription`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              code: subscription.subscription_code,
              token: subscription.email_token
            }),
          });

          if (cancelResponse.ok) {
            logger.log('[Account Deletion] Cancelled subscription:', subscription.subscription_code);
          } else {
            logger.error('[Account Deletion] Failed to cancel subscription:', subscription.subscription_code);
          }
        } catch (error) {
          logger.error('[Account Deletion] Error cancelling subscription:', subscription.subscription_code, error);
        }
      }
    }
  } catch (error) {
    logger.error('[Account Deletion] Error in cancelUserSubscriptions:', error);
    throw error;
  }
}

/**
 * Delete all user data from Firestore collections
 */
async function deleteUserDataFromFirestore(userId: string): Promise<Record<string, any>> {
  const results: Record<string, any> = {};

  // Collections that contain user data
  const collectionsToDelete = [
    'users',
    'userUsage',
    'flashcardSets',
    'subscriptions',
    'refundRequests'
  ];

  for (const collectionName of collectionsToDelete) {
    try {
      let deletedCount = 0;

      if (collectionName === 'users' || collectionName === 'userUsage') {
        // Direct document deletion for user-specific collections
        const docRef = adminDb.collection(collectionName).doc(userId);
        const doc = await docRef.get();

        if (doc.exists) {
          await docRef.delete();
          deletedCount = 1;
          logger.log(`[Account Deletion] Deleted ${collectionName} document for user:`, userId);
        }
      } else {
        // Query-based deletion for collections with userId field
        const query = adminDb.collection(collectionName).where('userId', '==', userId);
        const snapshot = await query.get();

        if (!snapshot.empty) {
          const batch = adminDb.batch();
          snapshot.docs.forEach((doc: any) => {
            batch.delete(doc.ref);
          });

          await batch.commit();
          deletedCount = snapshot.docs.length;
          logger.log(`[Account Deletion] Deleted ${deletedCount} documents from ${collectionName} for user:`, userId);
        }
      }

      results[collectionName] = {
        success: true,
        deletedCount
      };
    } catch (error) {
      logger.error(`[Account Deletion] Error deleting from ${collectionName}:`, error);
      results[collectionName] = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  return results;
}

/**
 * Send account deletion confirmation email
 * For now, this is a placeholder that logs the email content
 * In production, this would integrate with an email service like SendGrid
 */
async function sendAccountDeletionConfirmation(
  userEmail: string,
  userDisplayName: string | null
): Promise<void> {
  const emailContent = {
    to: userEmail,
    subject: 'Account Deletion Confirmation - Flash Cards AI',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Account Deletion Confirmation</h2>

        <p>Hello ${userDisplayName || 'User'},</p>

        <p>This email confirms that your Flash Cards AI account has been permanently deleted as requested.</p>

        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #333;">What was deleted:</h3>
          <ul>
            <li>Your user account and profile information</li>
            <li>All flashcard sets and generated content</li>
            <li>Usage history and statistics</li>
            <li>Subscription information (if applicable)</li>
            <li>Settings and preferences</li>
          </ul>
        </div>

        <p><strong>Important:</strong> This action is permanent and cannot be undone. If you had an active subscription, it has been cancelled and you will not be charged further.</p>

        <p>If you did not request this account deletion, please contact our support team <NAME_EMAIL></p>

        <p>Thank you for using Flash Cards AI.</p>

        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        <p style="font-size: 12px; color: #666;">
          This is an automated message from Flash Cards AI by Anker Studios.<br>
          Deletion completed on: ${new Date().toLocaleString()}
        </p>
      </div>
    `
  };

  // TODO: Integrate with actual email service (SendGrid, AWS SES, etc.)
  // For now, we'll log the email content for development
  logger.log('[Account Deletion] Email confirmation content prepared:', {
    to: emailContent.to,
    subject: emailContent.subject,
    htmlLength: emailContent.html.length
  });

  // In production, you would send the email here:
  // await emailService.send(emailContent);

  // For now, we'll just log that the email would be sent
  logger.log('[Account Deletion] Account deletion confirmation email would be sent to:', userEmail);
}
